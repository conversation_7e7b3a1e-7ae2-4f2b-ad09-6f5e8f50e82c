May-21 02:07:25.897 [main] DEBUG nextflow.cli.Launcher - $> nextflow run main.nf --genome=GCF_025823245.1.fna --batch_count=66 --outdir=results_GCF_025823245
May-21 02:07:26.100 [main] DEBUG nextflow.cli.CmdRun - N E X T F L O W  ~  version 25.04.2
May-21 02:07:26.132 [main] DEBUG nextflow.plugin.PluginsFacade - Setting up plugin manager > mode=prod; embedded=false; plugins-dir=/clusterfs/jgi/groups/science/homes/laureli/.nextflow/plugins; core-plugins: nf-amazon@2.15.0,nf-azure@1.16.0,nf-cloudcache@0.4.3,nf-codecommit@0.2.3,nf-console@1.2.1,nf-google@1.21.0,nf-k8s@1.0.0,nf-tower@1.11.2,nf-wave@1.12.1
May-21 02:07:26.155 [main] INFO  o.pf4j.DefaultPluginStatusProvider - Enabled plugins: []
May-21 02:07:26.156 [main] INFO  o.pf4j.DefaultPluginStatusProvider - Disabled plugins: []
May-21 02:07:26.159 [main] INFO  org.pf4j.DefaultPluginManager - PF4J version 3.12.0 in 'deployment' mode
May-21 02:07:26.173 [main] INFO  org.pf4j.AbstractPluginManager - No plugins
May-21 02:07:26.192 [main] DEBUG nextflow.config.ConfigBuilder - Found config local: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/nextflow.config
May-21 02:07:26.195 [main] DEBUG nextflow.config.ConfigBuilder - Parsing config file: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/nextflow.config
May-21 02:07:26.218 [main] DEBUG n.secret.LocalSecretsProvider - Secrets store: /clusterfs/jgi/groups/science/homes/laureli/.nextflow/secrets/store.json
May-21 02:07:26.221 [main] DEBUG nextflow.secret.SecretsLoader - Discovered secrets providers: [nextflow.secret.LocalSecretsProvider@1d01dfa5] - activable => nextflow.secret.LocalSecretsProvider@1d01dfa5
May-21 02:07:26.232 [main] DEBUG nextflow.config.ConfigBuilder - Applying config profile: `standard`
May-21 02:07:26.724 [main] DEBUG nextflow.cli.CmdRun - Applied DSL=2 from script declaration
May-21 02:07:26.739 [main] DEBUG nextflow.cli.CmdRun - Launching `main.nf` [naughty_faraday] DSL2 - revision: 7b2f86a5bc
May-21 02:07:26.741 [main] DEBUG nextflow.plugin.PluginsFacade - Plugins default=[]
May-21 02:07:26.741 [main] DEBUG nextflow.plugin.PluginsFacade - Plugins resolved requirement=[]
May-21 02:07:26.783 [main] DEBUG nextflow.Session - Session UUID: d45d4102-c75c-4a65-b221-8d35116c0f5d
May-21 02:07:26.784 [main] DEBUG nextflow.Session - Run name: naughty_faraday
May-21 02:07:26.784 [main] DEBUG nextflow.Session - Executor pool size: 32
May-21 02:07:26.791 [main] DEBUG nextflow.file.FilePorter - File porter settings maxRetries=3; maxTransfers=50; pollTimeout=null
May-21 02:07:26.796 [main] DEBUG nextflow.util.ThreadPoolBuilder - Creating thread pool 'FileTransfer' minSize=10; maxSize=96; workQueue=LinkedBlockingQueue[-1]; allowCoreThreadTimeout=false
May-21 02:07:26.823 [main] DEBUG nextflow.cli.CmdRun - 
  Version: 25.04.2 build 5947
  Created: 13-05-2025 21:58 UTC (14:58 PDT)
  System: Linux 4.18.0-553.5.1.el8_10.x86_64
  Runtime: Groovy 4.0.26 on OpenJDK 64-Bit Server VM 17.0.11-internal+0-adhoc..src
  Encoding: UTF-8 (UTF-8)
  Process: <EMAIL> [10.0.7.9]
  CPUs: 32 - Mem: 503.8 GB (10.6 GB) - Swap: 8 GB (2.7 GB)
May-21 02:07:26.867 [main] DEBUG nextflow.Session - Work-dir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work [nfs]
May-21 02:07:26.908 [main] DEBUG nextflow.executor.ExecutorFactory - Extension executors providers=[]
May-21 02:07:26.917 [main] DEBUG nextflow.Session - Observer factory: DefaultObserverFactory
May-21 02:07:26.922 [main] DEBUG nextflow.Session - Observer factory (v2): LinObserverFactory
May-21 02:07:26.944 [main] DEBUG nextflow.cache.CacheFactory - Using Nextflow cache factory: nextflow.cache.DefaultCacheFactory
May-21 02:07:26.952 [main] DEBUG nextflow.util.CustomThreadPool - Creating default thread pool > poolSize: 33; maxThreads: 1000
May-21 02:07:27.084 [main] DEBUG nextflow.Session - Session start
May-21 02:07:27.715 [main] DEBUG nextflow.script.ScriptRunner - > Launching execution
May-21 02:07:27.751 [main] INFO  nextflow.Nextflow - 
==============================================
 GENOMIC DATA PROCESSING WORKFLOW
==============================================
 input genome: GCF_025823245.1.fna
 outdir     : results_GCF_025823245
 gtdbtk_db  : /clusterfs/jgi/scratch/science/mgs/nelli/databases/gtdbtk/release220
 hmm_base_dir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/1_pangenomes_features/genera_pangenome_est_30_Sept/hmm_est_5_30_Sept
 processing batches: 1 to 66
 scaler path: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/robustscaler_enc1024_layers1.pkl
 model path : /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/robustscaler_enc1024_layers1.h5
 autoencoder output: encoded_features.csv
 ML model path: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/best_refined_model_RS.pkl
 prediction output: prediction_results.csv
 prediction version: 1
==============================================

May-21 02:07:27.853 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:PRODIGAL` matches process PRODIGAL
May-21 02:07:27.862 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: local
May-21 02:07:27.862 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
May-21 02:07:27.867 [main] DEBUG nextflow.executor.Executor - [warm up] executor > local
May-21 02:07:27.872 [main] DEBUG n.processor.LocalPollingMonitor - Creating local task monitor for executor 'local' > cpus=32; memory=503.8 GB; capacity=32; pollInterval=100ms; dumpInterval=5m
May-21 02:07:27.874 [main] DEBUG n.processor.TaskPollingMonitor - >>> barrier register (monitor: local)
May-21 02:07:27.894 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'PRODIGAL': maxForks=0; fair=false; array=0
May-21 02:07:27.935 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:GTDBTK` matches process GTDBTK
May-21 02:07:27.936 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: local
May-21 02:07:27.936 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
May-21 02:07:27.938 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'GTDBTK': maxForks=0; fair=false; array=0
May-21 02:07:27.958 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:HMMSEARCH` matches process HMMSEARCH
May-21 02:07:27.977 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: local
May-21 02:07:27.977 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
May-21 02:07:27.978 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'HMMSEARCH': maxForks=0; fair=false; array=0
May-21 02:07:27.980 [main] DEBUG nextflow.processor.TaskProcessor - Creating *combiner* operator for each param(s) at index(es): [0, 1, 2]
May-21 02:07:27.992 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:PROCESS_HITS` matches process PROCESS_HITS
May-21 02:07:27.994 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: local
May-21 02:07:27.994 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
May-21 02:07:27.995 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'PROCESS_HITS': maxForks=0; fair=false; array=0
May-21 02:07:27.996 [main] DEBUG nextflow.processor.TaskProcessor - Creating *combiner* operator for each param(s) at index(es): [1]
May-21 02:07:28.004 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:CREATE_DATAFRAME` matches process CREATE_DATAFRAME
May-21 02:07:28.006 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: local
May-21 02:07:28.006 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
May-21 02:07:28.007 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'CREATE_DATAFRAME': maxForks=0; fair=false; array=0
May-21 02:07:28.016 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: local
May-21 02:07:28.016 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
May-21 02:07:28.016 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'AUTOENCODER': maxForks=0; fair=false; array=0
May-21 02:07:28.022 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: local
May-21 02:07:28.022 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
May-21 02:07:28.023 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'MODEL_PREDICT': maxForks=0; fair=false; array=0
May-21 02:07:28.026 [main] DEBUG nextflow.Session - Workflow process names [dsl2]: CREATE_DATAFRAME, MODEL_PREDICT, GTDBTK, HMMSEARCH, PRODIGAL, PROCESS_HITS, AUTOENCODER
May-21 02:07:28.029 [main] DEBUG nextflow.Session - Igniting dataflow network (21)
May-21 02:07:28.035 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > PRODIGAL
May-21 02:07:28.036 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > GTDBTK
May-21 02:07:28.036 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > HMMSEARCH
May-21 02:07:28.036 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > HMMSEARCH
May-21 02:07:28.036 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > PROCESS_HITS
May-21 02:07:28.036 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > PROCESS_HITS
May-21 02:07:28.037 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > CREATE_DATAFRAME
May-21 02:07:28.037 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > AUTOENCODER
May-21 02:07:28.037 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > MODEL_PREDICT
May-21 02:07:28.037 [main] DEBUG nextflow.script.ScriptRunner - Parsed script files:
  Script_3b45fda2277c14a8: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/main.nf
May-21 02:07:28.037 [main] DEBUG nextflow.script.ScriptRunner - > Awaiting termination 
May-21 02:07:28.037 [main] DEBUG nextflow.Session - Session await
May-21 02:07:28.201 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:07:28.203 [Task submitter] INFO  nextflow.Session - [0c/4b939e] Submitted process > GTDBTK (1)
May-21 02:07:28.227 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:07:28.227 [Task submitter] INFO  nextflow.Session - [81/492dfc] Submitted process > PRODIGAL (1)
May-21 02:07:37.779 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 1; name: PRODIGAL (1); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/81/492dfc6a3f2433c4d6e329e7171a3d]
May-21 02:07:37.781 [Task monitor] DEBUG nextflow.util.ThreadPoolBuilder - Creating thread pool 'TaskFinalizer' minSize=10; maxSize=96; workQueue=LinkedBlockingQueue[-1]; allowCoreThreadTimeout=false
May-21 02:07:37.807 [TaskFinalizer-1] DEBUG nextflow.util.ThreadPoolBuilder - Creating thread pool 'PublishDir' minSize=10; maxSize=96; workQueue=LinkedBlockingQueue[-1]; allowCoreThreadTimeout=false
May-21 02:07:37.852 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:07:37.853 [Task submitter] INFO  nextflow.Session - [b0/7877a0] Submitted process > HMMSEARCH (batch_9)
May-21 02:07:44.239 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 11; name: HMMSEARCH (batch_9); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/b0/7877a065d83473aca6c5c070c98757]
May-21 02:07:44.266 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:07:44.266 [Task submitter] INFO  nextflow.Session - [ef/e37a2f] Submitted process > HMMSEARCH (batch_23)
May-21 02:07:50.589 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 25; name: HMMSEARCH (batch_23); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/ef/e37a2f6156b1d23ed1dc81ae89dd1a]
May-21 02:07:50.601 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:07:50.601 [Task submitter] INFO  nextflow.Session - [33/f0036d] Submitted process > HMMSEARCH (batch_11)
May-21 02:07:56.546 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 13; name: HMMSEARCH (batch_11); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/33/f0036da0ecbcdef4a56e14a3c7d0c0]
May-21 02:07:56.559 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:07:56.559 [Task submitter] INFO  nextflow.Session - [99/6bdcc1] Submitted process > HMMSEARCH (batch_14)
May-21 02:08:02.910 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 16; name: HMMSEARCH (batch_14); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/99/6bdcc1b2399d1db609e4ba230034d6]
May-21 02:08:02.921 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:08:02.921 [Task submitter] INFO  nextflow.Session - [f7/3288ae] Submitted process > HMMSEARCH (batch_30)
May-21 02:08:10.036 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 32; name: HMMSEARCH (batch_30); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/f7/3288ae13aa6dcec9e7b7826d383ed6]
May-21 02:08:10.051 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:08:10.052 [Task submitter] INFO  nextflow.Session - [1d/a86cd0] Submitted process > HMMSEARCH (batch_4)
May-21 02:08:17.179 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 6; name: HMMSEARCH (batch_4); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/1d/a86cd09fef91c44330b64cde83898d]
May-21 02:08:17.192 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:08:17.192 [Task submitter] INFO  nextflow.Session - [1b/0282ed] Submitted process > HMMSEARCH (batch_13)
May-21 02:08:26.092 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 15; name: HMMSEARCH (batch_13); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/1b/0282edc71fea673b606b705f05330c]
May-21 02:08:26.120 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:08:26.121 [Task submitter] INFO  nextflow.Session - [82/a8df4b] Submitted process > HMMSEARCH (batch_5)
May-21 02:08:34.215 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 7; name: HMMSEARCH (batch_5); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/82/a8df4b6d8a2315cdfb5684b92c557c]
May-21 02:08:34.226 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:08:34.227 [Task submitter] INFO  nextflow.Session - [29/d76cd4] Submitted process > HMMSEARCH (batch_32)
May-21 02:08:42.200 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 34; name: HMMSEARCH (batch_32); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/29/d76cd49c7388acbefdcdc0ee3e8604]
May-21 02:08:42.230 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:08:42.230 [Task submitter] INFO  nextflow.Session - [37/b63fbf] Submitted process > HMMSEARCH (batch_8)
May-21 02:08:50.661 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 10; name: HMMSEARCH (batch_8); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/37/b63fbf0621cf198574a5d6293264b7]
May-21 02:08:50.681 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:08:50.682 [Task submitter] INFO  nextflow.Session - [6b/403da6] Submitted process > HMMSEARCH (batch_28)
May-21 02:08:58.665 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 30; name: HMMSEARCH (batch_28); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/6b/403da69a44c7139bf10c4aa4d8df2c]
May-21 02:08:58.697 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:08:58.697 [Task submitter] INFO  nextflow.Session - [10/49ae13] Submitted process > HMMSEARCH (batch_15)
May-21 02:09:06.431 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 17; name: HMMSEARCH (batch_15); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/10/49ae13cdf64e8d4c7166ad9ee0b93a]
May-21 02:09:06.468 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:09:06.468 [Task submitter] INFO  nextflow.Session - [80/d40760] Submitted process > HMMSEARCH (batch_39)
May-21 02:09:13.809 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 41; name: HMMSEARCH (batch_39); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/80/d40760e568942d9bbbcca81dd26f53]
May-21 02:09:13.831 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:09:13.831 [Task submitter] INFO  nextflow.Session - [b6/8b3bc7] Submitted process > HMMSEARCH (batch_6)
May-21 02:09:20.710 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 8; name: HMMSEARCH (batch_6); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/b6/8b3bc754fcf7a8c2ce3b62d35a4fc5]
May-21 02:09:20.729 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:09:20.729 [Task submitter] INFO  nextflow.Session - [82/12b06a] Submitted process > HMMSEARCH (batch_17)
May-21 02:09:27.094 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 19; name: HMMSEARCH (batch_17); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/82/12b06abb4ab6b52487a4c9126894ae]
May-21 02:09:27.107 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:09:27.108 [Task submitter] INFO  nextflow.Session - [9f/01b358] Submitted process > HMMSEARCH (batch_22)
May-21 02:09:33.408 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 24; name: HMMSEARCH (batch_22); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/9f/01b358e197faae5ce6465f6c68f9eb]
May-21 02:09:33.419 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:09:33.420 [Task submitter] INFO  nextflow.Session - [ad/6d0ec3] Submitted process > HMMSEARCH (batch_18)
May-21 02:09:40.147 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 20; name: HMMSEARCH (batch_18); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/ad/6d0ec385c2d656c3b5187952b2d9de]
May-21 02:09:40.159 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:09:40.159 [Task submitter] INFO  nextflow.Session - [05/ec6af3] Submitted process > HMMSEARCH (batch_38)
May-21 02:09:46.368 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 40; name: HMMSEARCH (batch_38); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/05/ec6af38c4fbc522278152dfefea61a]
May-21 02:09:46.382 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:09:46.383 [Task submitter] INFO  nextflow.Session - [3d/2ecf8c] Submitted process > HMMSEARCH (batch_19)
May-21 02:09:52.558 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 21; name: HMMSEARCH (batch_19); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/3d/2ecf8c93a8e47efa501681c20ecbd7]
May-21 02:09:52.570 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:09:52.570 [Task submitter] INFO  nextflow.Session - [fa/8edc9b] Submitted process > HMMSEARCH (batch_21)
May-21 02:09:58.932 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 23; name: HMMSEARCH (batch_21); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/fa/8edc9b603bd9f12a6d5d77a8069ad2]
May-21 02:09:58.969 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:09:58.969 [Task submitter] INFO  nextflow.Session - [a2/1ba468] Submitted process > HMMSEARCH (batch_29)
May-21 02:10:06.742 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 31; name: HMMSEARCH (batch_29); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/a2/1ba468ee52d5d5a453602a3e7bd112]
May-21 02:10:06.784 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:10:06.784 [Task submitter] INFO  nextflow.Session - [d4/765543] Submitted process > HMMSEARCH (batch_25)
May-21 02:10:12.715 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 27; name: HMMSEARCH (batch_25); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/d4/76554355740eaf5a81504a540e2d7a]
May-21 02:10:12.728 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:10:12.729 [Task submitter] INFO  nextflow.Session - [2e/2730f7] Submitted process > HMMSEARCH (batch_26)
May-21 02:10:18.424 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 28; name: HMMSEARCH (batch_26); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/2e/2730f77633d2b3fc23949de1970cad]
May-21 02:10:18.435 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:10:18.435 [Task submitter] INFO  nextflow.Session - [7b/24f721] Submitted process > HMMSEARCH (batch_7)
May-21 02:10:24.782 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 9; name: HMMSEARCH (batch_7); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/7b/24f72135d10107c703fc8dff1e59c8]
May-21 02:10:24.803 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:10:24.803 [Task submitter] INFO  nextflow.Session - [90/455613] Submitted process > HMMSEARCH (batch_16)
May-21 02:10:31.751 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 18; name: HMMSEARCH (batch_16); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/90/455613042c746a9c6c163963f29614]
May-21 02:10:31.768 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:10:31.768 [Task submitter] INFO  nextflow.Session - [a9/3846eb] Submitted process > HMMSEARCH (batch_24)
May-21 02:10:39.196 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 26; name: HMMSEARCH (batch_24); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/a9/3846ebb333b1d067fc52c44f7ea168]
May-21 02:10:39.218 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:10:39.218 [Task submitter] INFO  nextflow.Session - [b6/0b1408] Submitted process > HMMSEARCH (batch_12)
May-21 02:10:48.897 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 14; name: HMMSEARCH (batch_12); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/b6/0b1408848356e7080c99c077479925]
May-21 02:10:48.933 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:10:48.933 [Task submitter] INFO  nextflow.Session - [02/fedba1] Submitted process > HMMSEARCH (batch_34)
May-21 02:11:00.098 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 36; name: HMMSEARCH (batch_34); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/02/fedba1ba00edb5238fb09ccbb57821]
May-21 02:11:00.114 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:11:00.115 [Task submitter] INFO  nextflow.Session - [f4/fc22f9] Submitted process > HMMSEARCH (batch_33)
May-21 02:11:10.089 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 35; name: HMMSEARCH (batch_33); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/f4/fc22f9c64a3504a9a54e79f34cb56e]
May-21 02:11:10.119 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:11:10.119 [Task submitter] INFO  nextflow.Session - [90/b45b01] Submitted process > HMMSEARCH (batch_2)
May-21 02:11:20.072 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 4; name: HMMSEARCH (batch_2); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/90/b45b01730a83fd50f4ef6b1cc48f7a]
May-21 02:11:20.144 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:11:20.144 [Task submitter] INFO  nextflow.Session - [1a/03063a] Submitted process > HMMSEARCH (batch_35)
May-21 02:11:29.534 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 37; name: HMMSEARCH (batch_35); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/1a/03063a76eb8d73be631fed716e7618]
May-21 02:11:29.591 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:11:29.591 [Task submitter] INFO  nextflow.Session - [5e/32f0dd] Submitted process > HMMSEARCH (batch_20)
May-21 02:11:44.253 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 22; name: HMMSEARCH (batch_20); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/5e/32f0dd640277447d8ccd853dac9eff]
May-21 02:11:44.273 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:11:44.273 [Task submitter] INFO  nextflow.Session - [e1/fac772] Submitted process > HMMSEARCH (batch_10)
May-21 02:11:53.471 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 12; name: HMMSEARCH (batch_10); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/e1/fac7729653c9118e5844fcd7dbff2d]
May-21 02:11:53.486 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:11:53.487 [Task submitter] INFO  nextflow.Session - [bf/e42552] Submitted process > HMMSEARCH (batch_27)
May-21 02:12:04.313 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 29; name: HMMSEARCH (batch_27); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/bf/e42552ccf95f4a30db594b8973f032]
May-21 02:12:04.327 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:12:04.327 [Task submitter] INFO  nextflow.Session - [74/be5a87] Submitted process > HMMSEARCH (batch_55)
May-21 02:12:13.159 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 57; name: HMMSEARCH (batch_55); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/74/be5a87b13abbe81fdb4916828da41d]
May-21 02:12:13.175 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:12:13.176 [Task submitter] INFO  nextflow.Session - [0e/0efe78] Submitted process > HMMSEARCH (batch_1)
May-21 02:12:24.149 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 3; name: HMMSEARCH (batch_1); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/0e/0efe78ac9b6e25cf1cf8043ec6c4f0]
May-21 02:12:24.174 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:12:24.174 [Task submitter] INFO  nextflow.Session - [c1/9e7d42] Submitted process > HMMSEARCH (batch_37)
May-21 02:12:28.055 [Task monitor] DEBUG n.processor.TaskPollingMonitor - !! executor local > tasks to be completed: 2 -- submitted tasks are shown below
~> TaskHandler[id: 2; name: GTDBTK (1); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/0c/4b939e79e55a58e7b27aaf1f4b6cdf]
~> TaskHandler[id: 39; name: HMMSEARCH (batch_37); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/c1/9e7d426180ea9de115d23e1998f638]
May-21 02:12:35.516 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 39; name: HMMSEARCH (batch_37); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/c1/9e7d426180ea9de115d23e1998f638]
May-21 02:12:35.531 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:12:35.531 [Task submitter] INFO  nextflow.Session - [19/f9a117] Submitted process > HMMSEARCH (batch_42)
May-21 02:12:38.533 [Task submitter] DEBUG n.processor.TaskPollingMonitor - %% executor local > tasks in the submission queue: 65 -- tasks to be submitted are shown below
~> TaskHandler[id: 42; name: HMMSEARCH (batch_40); status: NEW; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/9e/fd59f383a88467fb71b964e1fb41e4]
~> TaskHandler[id: 5; name: HMMSEARCH (batch_3); status: NEW; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/0a/1c1f4ee4ea7393882a13c2b082ea62]
~> TaskHandler[id: 38; name: HMMSEARCH (batch_36); status: NEW; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/4b/9f4be2672d907806e0153cb84da478]
~> TaskHandler[id: 33; name: HMMSEARCH (batch_31); status: NEW; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/b6/ba7e4622b62f37e52a0b01bd2b4d03]
~> TaskHandler[id: 43; name: HMMSEARCH (batch_41); status: NEW; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/60/1afe32261eee995594c19cec0d7e51]
~> TaskHandler[id: 47; name: HMMSEARCH (batch_45); status: NEW; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/e1/eb74e8ddb5e10d4581d0e3de46240d]
~> TaskHandler[id: 49; name: HMMSEARCH (batch_47); status: NEW; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/61/96c09aaa6c8a3e8bf3e0dd47f115e5]
~> TaskHandler[id: 46; name: HMMSEARCH (batch_44); status: NEW; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/d3/e9e47bad92b3c6af481804f25e3e94]
~> TaskHandler[id: 45; name: HMMSEARCH (batch_43); status: NEW; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/ce/2cfc7b88eba42e557e246c37ba591c]
~> TaskHandler[id: 48; name: HMMSEARCH (batch_46); status: NEW; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/32/6c8c8b249d80167f6b08596d676e13]
.. remaining tasks omitted.
May-21 02:12:48.233 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 44; name: HMMSEARCH (batch_42); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/19/f9a117e4756c82359ac221334497e0]
May-21 02:12:48.249 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:12:48.249 [Task submitter] INFO  nextflow.Session - [9e/fd59f3] Submitted process > HMMSEARCH (batch_40)
May-21 02:12:56.985 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 42; name: HMMSEARCH (batch_40); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/9e/fd59f383a88467fb71b964e1fb41e4]
May-21 02:12:57.003 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:12:57.004 [Task submitter] INFO  nextflow.Session - [0a/1c1f4e] Submitted process > HMMSEARCH (batch_3)
May-21 02:13:04.752 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 5; name: HMMSEARCH (batch_3); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/0a/1c1f4ee4ea7393882a13c2b082ea62]
May-21 02:13:04.771 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:13:04.771 [Task submitter] INFO  nextflow.Session - [4b/9f4be2] Submitted process > HMMSEARCH (batch_36)
May-21 02:13:15.703 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 38; name: HMMSEARCH (batch_36); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/4b/9f4be2672d907806e0153cb84da478]
May-21 02:13:15.721 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:13:15.722 [Task submitter] INFO  nextflow.Session - [b6/ba7e46] Submitted process > HMMSEARCH (batch_31)
May-21 02:13:27.780 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 33; name: HMMSEARCH (batch_31); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/b6/ba7e4622b62f37e52a0b01bd2b4d03]
May-21 02:13:27.837 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:13:27.838 [Task submitter] INFO  nextflow.Session - [60/1afe32] Submitted process > HMMSEARCH (batch_41)
May-21 02:13:38.915 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 43; name: HMMSEARCH (batch_41); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/60/1afe32261eee995594c19cec0d7e51]
May-21 02:13:38.936 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:13:38.936 [Task submitter] INFO  nextflow.Session - [e1/eb74e8] Submitted process > HMMSEARCH (batch_45)
May-21 02:13:47.831 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 47; name: HMMSEARCH (batch_45); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/e1/eb74e8ddb5e10d4581d0e3de46240d]
May-21 02:13:47.846 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:13:47.846 [Task submitter] INFO  nextflow.Session - [61/96c09a] Submitted process > HMMSEARCH (batch_47)
May-21 02:13:56.626 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 49; name: HMMSEARCH (batch_47); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/61/96c09aaa6c8a3e8bf3e0dd47f115e5]
May-21 02:13:56.660 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:13:56.661 [Task submitter] INFO  nextflow.Session - [d3/e9e47b] Submitted process > HMMSEARCH (batch_44)
May-21 02:14:05.375 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 46; name: HMMSEARCH (batch_44); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/d3/e9e47bad92b3c6af481804f25e3e94]
May-21 02:14:05.392 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:14:05.393 [Task submitter] INFO  nextflow.Session - [ce/2cfc7b] Submitted process > HMMSEARCH (batch_43)
May-21 02:14:14.248 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 45; name: HMMSEARCH (batch_43); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/ce/2cfc7b88eba42e557e246c37ba591c]
May-21 02:14:14.260 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:14:14.260 [Task submitter] INFO  nextflow.Session - [32/6c8c8b] Submitted process > HMMSEARCH (batch_46)
May-21 02:14:24.966 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 48; name: HMMSEARCH (batch_46); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/32/6c8c8b249d80167f6b08596d676e13]
May-21 02:14:24.998 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:14:24.999 [Task submitter] INFO  nextflow.Session - [29/5f713e] Submitted process > HMMSEARCH (batch_51)
May-21 02:14:34.786 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 53; name: HMMSEARCH (batch_51); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/29/5f713e84e21b71fd09f6622059123a]
May-21 02:14:34.811 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:14:34.811 [Task submitter] INFO  nextflow.Session - [39/1efdeb] Submitted process > HMMSEARCH (batch_49)
May-21 02:14:43.078 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 51; name: HMMSEARCH (batch_49); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/39/1efdebfd63623a8cffb936830fcb53]
May-21 02:14:43.096 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:14:43.096 [Task submitter] INFO  nextflow.Session - [70/deffbc] Submitted process > HMMSEARCH (batch_52)
May-21 02:14:59.812 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 54; name: HMMSEARCH (batch_52); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/70/deffbc230e38a4014d341850da194c]
May-21 02:14:59.833 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:14:59.834 [Task submitter] INFO  nextflow.Session - [57/c7278e] Submitted process > HMMSEARCH (batch_48)
May-21 02:15:12.068 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 50; name: HMMSEARCH (batch_48); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/57/c7278eab9c542f0a9a0455130e6cc0]
May-21 02:15:12.083 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:15:12.083 [Task submitter] INFO  nextflow.Session - [97/f5d061] Submitted process > HMMSEARCH (batch_50)
May-21 02:15:21.511 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 52; name: HMMSEARCH (batch_50); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/97/f5d06167afe259384e807b7d4722eb]
May-21 02:15:21.525 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:15:21.525 [Task submitter] INFO  nextflow.Session - [b8/cfe063] Submitted process > HMMSEARCH (batch_53)
May-21 02:15:29.221 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 55; name: HMMSEARCH (batch_53); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/b8/cfe0639f55f6dce4a31a4357f1cc98]
May-21 02:15:29.234 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:15:29.234 [Task submitter] INFO  nextflow.Session - [71/80bb34] Submitted process > HMMSEARCH (batch_56)
May-21 02:15:35.436 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 58; name: HMMSEARCH (batch_56); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/71/80bb342e4c73568849c73e6eb033c3]
May-21 02:15:35.450 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:15:35.450 [Task submitter] INFO  nextflow.Session - [65/859559] Submitted process > HMMSEARCH (batch_54)
May-21 02:15:42.252 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 56; name: HMMSEARCH (batch_54); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/65/85955914fa1ec5e411d5ead0b50229]
May-21 02:15:42.263 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:15:42.264 [Task submitter] INFO  nextflow.Session - [d9/888c4c] Submitted process > HMMSEARCH (batch_60)
May-21 02:15:51.560 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 62; name: HMMSEARCH (batch_60); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/d9/888c4c9d3ee86d8a9062fe131b4d64]
May-21 02:15:51.598 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:15:51.599 [Task submitter] INFO  nextflow.Session - [a1/cd2e47] Submitted process > HMMSEARCH (batch_59)
May-21 02:15:58.587 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 61; name: HMMSEARCH (batch_59); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/a1/cd2e4733a67a02099d80028c955558]
May-21 02:15:58.598 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:15:58.598 [Task submitter] INFO  nextflow.Session - [c6/b82672] Submitted process > HMMSEARCH (batch_61)
May-21 02:16:06.181 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 63; name: HMMSEARCH (batch_61); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/c6/b82672e447932318be1fd0f6bbe9e6]
May-21 02:16:06.192 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:16:06.193 [Task submitter] INFO  nextflow.Session - [ae/368504] Submitted process > HMMSEARCH (batch_57)
May-21 02:16:12.716 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 59; name: HMMSEARCH (batch_57); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/ae/36850486a73d63d570cfdf54325947]
May-21 02:16:12.728 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:16:12.728 [Task submitter] INFO  nextflow.Session - [b1/1712a1] Submitted process > HMMSEARCH (batch_66)
May-21 02:16:13.992 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 68; name: HMMSEARCH (batch_66); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/b1/1712a1592804b1a8b19a0db3679cfb]
May-21 02:16:14.028 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:16:14.028 [Task submitter] INFO  nextflow.Session - [26/016685] Submitted process > HMMSEARCH (batch_65)
May-21 02:16:21.280 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 67; name: HMMSEARCH (batch_65); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/26/016685f3de701aed39f0d42e7f8596]
May-21 02:16:21.293 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:16:21.294 [Task submitter] INFO  nextflow.Session - [34/828d63] Submitted process > HMMSEARCH (batch_64)
May-21 02:16:28.971 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 66; name: HMMSEARCH (batch_64); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/34/828d63de031ea3309c119b0a2ef26f]
May-21 02:16:28.988 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:16:28.988 [Task submitter] INFO  nextflow.Session - [1b/d42b33] Submitted process > HMMSEARCH (batch_58)
May-21 02:16:37.432 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 60; name: HMMSEARCH (batch_58); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/1b/d42b33e6336841f18e4ba6bec2aa26]
May-21 02:16:37.445 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:16:37.446 [Task submitter] INFO  nextflow.Session - [cc/009036] Submitted process > HMMSEARCH (batch_63)
May-21 02:16:46.447 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 65; name: HMMSEARCH (batch_63); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/cc/009036e64265e6987d9aad3d02c951]
May-21 02:16:46.495 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:16:46.495 [Task submitter] INFO  nextflow.Session - [4e/3bcec2] Submitted process > HMMSEARCH (batch_62)
May-21 02:16:54.163 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 64; name: HMMSEARCH (batch_62); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/4e/3bcec252138c00600b1e8f7854686e]
May-21 02:16:54.176 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:16:54.176 [Task submitter] INFO  nextflow.Session - [e6/eb4fa7] Submitted process > PROCESS_HITS (batch_9)
May-21 02:16:54.193 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:16:54.193 [Task submitter] INFO  nextflow.Session - [b6/7c0b43] Submitted process > PROCESS_HITS (batch_23)
May-21 02:16:54.215 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:16:54.215 [Task submitter] INFO  nextflow.Session - [da/fc49cb] Submitted process > PROCESS_HITS (batch_11)
May-21 02:16:54.228 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:16:54.228 [Task submitter] INFO  nextflow.Session - [61/d221f7] Submitted process > PROCESS_HITS (batch_14)
May-21 02:16:54.242 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:16:54.242 [Task submitter] INFO  nextflow.Session - [44/f58ba1] Submitted process > PROCESS_HITS (batch_30)
May-21 02:16:54.255 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:16:54.255 [Task submitter] INFO  nextflow.Session - [42/05302d] Submitted process > PROCESS_HITS (batch_4)
May-21 02:16:54.267 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:16:54.267 [Task submitter] INFO  nextflow.Session - [1c/407f8a] Submitted process > PROCESS_HITS (batch_13)
May-21 02:16:54.280 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:16:54.280 [Task submitter] INFO  nextflow.Session - [00/9c48ee] Submitted process > PROCESS_HITS (batch_5)
May-21 02:17:07.109 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 73; name: PROCESS_HITS (batch_30); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/44/f58ba18234ef75495e7a24d01944a5]
May-21 02:17:07.128 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:17:07.128 [Task submitter] INFO  nextflow.Session - [46/d34cea] Submitted process > PROCESS_HITS (batch_32)
May-21 02:17:07.277 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 74; name: PROCESS_HITS (batch_4); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/42/05302dfccbca2606e51caf5cde4b79]
May-21 02:17:07.291 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:17:07.291 [Task submitter] INFO  nextflow.Session - [bf/a67aa6] Submitted process > PROCESS_HITS (batch_8)
May-21 02:17:07.333 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 76; name: PROCESS_HITS (batch_5); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/00/9c48eedc17155e01ceb724029761b3]
May-21 02:17:07.355 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:17:07.355 [Task submitter] INFO  nextflow.Session - [e0/ce3470] Submitted process > PROCESS_HITS (batch_28)
May-21 02:17:07.372 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 75; name: PROCESS_HITS (batch_13); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/1c/407f8a09cc19d2adf6b2473affd07c]
May-21 02:17:07.389 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:17:07.389 [Task submitter] INFO  nextflow.Session - [74/05807e] Submitted process > PROCESS_HITS (batch_15)
May-21 02:17:09.223 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 72; name: PROCESS_HITS (batch_14); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/61/d221f71965d5fc9a72901c50ad4f6b]
May-21 02:17:09.234 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:17:09.234 [Task submitter] INFO  nextflow.Session - [0f/15d1f1] Submitted process > PROCESS_HITS (batch_39)
May-21 02:17:09.263 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 69; name: PROCESS_HITS (batch_9); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/e6/eb4fa76a5da7efc66ad0c49e60f038]
May-21 02:17:09.283 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:17:09.283 [Task submitter] INFO  nextflow.Session - [50/fbdebc] Submitted process > PROCESS_HITS (batch_6)
May-21 02:17:09.307 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 71; name: PROCESS_HITS (batch_11); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/da/fc49cb09480bf66a15e06f802e8d4c]
May-21 02:17:09.332 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:17:09.332 [Task submitter] INFO  nextflow.Session - [83/b2775b] Submitted process > PROCESS_HITS (batch_17)
May-21 02:17:09.446 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 70; name: PROCESS_HITS (batch_23); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/b6/7c0b432086ff7ae5bd02b8cd83c30c]
May-21 02:17:09.466 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:17:09.466 [Task submitter] INFO  nextflow.Session - [fd/c558a7] Submitted process > PROCESS_HITS (batch_22)
May-21 02:17:19.933 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 77; name: PROCESS_HITS (batch_32); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/46/d34ceab2b508519fa4ac05486b7cbb]
May-21 02:17:19.956 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:17:19.956 [Task submitter] INFO  nextflow.Session - [b8/4fa2c2] Submitted process > PROCESS_HITS (batch_18)
May-21 02:17:20.000 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 79; name: PROCESS_HITS (batch_28); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/e0/ce34705e7532bc5e12f00a5a5f131f]
May-21 02:17:20.028 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:17:20.028 [Task submitter] INFO  nextflow.Session - [9d/72c2fc] Submitted process > PROCESS_HITS (batch_38)
May-21 02:17:20.246 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 80; name: PROCESS_HITS (batch_15); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/74/05807ed645159ea8297a69d5e96b02]
May-21 02:17:20.269 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:17:20.270 [Task submitter] INFO  nextflow.Session - [13/f69d09] Submitted process > PROCESS_HITS (batch_19)
May-21 02:17:20.340 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 78; name: PROCESS_HITS (batch_8); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/bf/a67aa615aa501b46ca19adeaf44e0f]
May-21 02:17:20.374 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:17:20.374 [Task submitter] INFO  nextflow.Session - [87/8aa614] Submitted process > PROCESS_HITS (batch_21)
May-21 02:17:21.701 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 82; name: PROCESS_HITS (batch_6); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/50/fbdebcf1d59bad5bb3a43af5e04669]
May-21 02:17:21.723 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:17:21.723 [Task submitter] INFO  nextflow.Session - [4e/b825d5] Submitted process > PROCESS_HITS (batch_29)
May-21 02:17:21.892 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 81; name: PROCESS_HITS (batch_39); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/0f/15d1f1b2d206b80ce8f302717a159d]
May-21 02:17:21.906 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:17:21.907 [Task submitter] INFO  nextflow.Session - [cc/b42c0d] Submitted process > PROCESS_HITS (batch_25)
May-21 02:17:21.907 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 83; name: PROCESS_HITS (batch_17); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/83/b2775b3a323a9755ef028bd598cebc]
May-21 02:17:21.929 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:17:21.929 [Task submitter] INFO  nextflow.Session - [3c/393a19] Submitted process > PROCESS_HITS (batch_26)
May-21 02:17:22.405 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 84; name: PROCESS_HITS (batch_22); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/fd/c558a7602035c6a744e88892ac47c4]
May-21 02:17:22.420 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:17:22.421 [Task submitter] INFO  nextflow.Session - [f3/b8e65b] Submitted process > PROCESS_HITS (batch_7)
May-21 02:17:28.109 [Task monitor] DEBUG n.processor.TaskPollingMonitor - !! executor local > tasks to be completed: 9 -- submitted tasks are shown below
~> TaskHandler[id: 2; name: GTDBTK (1); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/0c/4b939e79e55a58e7b27aaf1f4b6cdf]
~> TaskHandler[id: 85; name: PROCESS_HITS (batch_18); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/b8/4fa2c26b3acb735e2f5e0a444835c7]
~> TaskHandler[id: 86; name: PROCESS_HITS (batch_38); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/9d/72c2fc41ab2a30881359adfc6d1481]
~> TaskHandler[id: 87; name: PROCESS_HITS (batch_19); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/13/f69d096e6e1cd6f32f07b5b748d3c4]
~> TaskHandler[id: 88; name: PROCESS_HITS (batch_21); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/87/8aa614edec8b0a6021c46c873f5877]
~> TaskHandler[id: 89; name: PROCESS_HITS (batch_29); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/4e/b825d526c442dba0b7137447aaa703]
~> TaskHandler[id: 90; name: PROCESS_HITS (batch_25); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/cc/b42c0ddac79bcdff2ec5ec0ab433c5]
~> TaskHandler[id: 91; name: PROCESS_HITS (batch_26); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/3c/393a190864e0589d767bcfea1d0fea]
~> TaskHandler[id: 92; name: PROCESS_HITS (batch_7); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/f3/b8e65b21c112cd5204947d941545c4]
May-21 02:17:33.150 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 85; name: PROCESS_HITS (batch_18); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/b8/4fa2c26b3acb735e2f5e0a444835c7]
May-21 02:17:33.170 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:17:33.170 [Task submitter] INFO  nextflow.Session - [af/8a25e4] Submitted process > PROCESS_HITS (batch_16)
May-21 02:17:33.200 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 86; name: PROCESS_HITS (batch_38); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/9d/72c2fc41ab2a30881359adfc6d1481]
May-21 02:17:33.218 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:17:33.224 [Task submitter] INFO  nextflow.Session - [20/37346a] Submitted process > PROCESS_HITS (batch_24)
May-21 02:17:33.533 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 87; name: PROCESS_HITS (batch_19); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/13/f69d096e6e1cd6f32f07b5b748d3c4]
May-21 02:17:33.569 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:17:33.569 [Task submitter] INFO  nextflow.Session - [62/7a3336] Submitted process > PROCESS_HITS (batch_12)
May-21 02:17:33.783 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 88; name: PROCESS_HITS (batch_21); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/87/8aa614edec8b0a6021c46c873f5877]
May-21 02:17:33.828 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:17:33.828 [Task submitter] INFO  nextflow.Session - [fe/6f9177] Submitted process > PROCESS_HITS (batch_34)
May-21 02:17:36.026 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 91; name: PROCESS_HITS (batch_26); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/3c/393a190864e0589d767bcfea1d0fea]
May-21 02:17:36.049 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:17:36.049 [Task submitter] INFO  nextflow.Session - [12/6d7c18] Submitted process > PROCESS_HITS (batch_33)
May-21 02:17:36.333 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 89; name: PROCESS_HITS (batch_29); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/4e/b825d526c442dba0b7137447aaa703]
May-21 02:17:36.362 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:17:36.362 [Task submitter] INFO  nextflow.Session - [ab/bf3ede] Submitted process > PROCESS_HITS (batch_2)
May-21 02:17:36.375 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 90; name: PROCESS_HITS (batch_25); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/cc/b42c0ddac79bcdff2ec5ec0ab433c5]
May-21 02:17:36.405 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:17:36.406 [Task submitter] INFO  nextflow.Session - [bf/c5abd0] Submitted process > PROCESS_HITS (batch_35)
May-21 02:17:36.830 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 92; name: PROCESS_HITS (batch_7); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/f3/b8e65b21c112cd5204947d941545c4]
May-21 02:17:36.868 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:17:36.868 [Task submitter] INFO  nextflow.Session - [7a/51bbdb] Submitted process > PROCESS_HITS (batch_20)
May-21 02:17:38.870 [Task submitter] DEBUG n.processor.TaskPollingMonitor - %% executor local > tasks in the submission queue: 34 -- tasks to be submitted are shown below
~> TaskHandler[id: 101; name: PROCESS_HITS (batch_10); status: NEW; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/b1/d26371a396d88e81cb0a0cf9d1db12]
~> TaskHandler[id: 102; name: PROCESS_HITS (batch_27); status: NEW; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/ba/1ae73197f4b12e2d25470881ab1b55]
~> TaskHandler[id: 103; name: PROCESS_HITS (batch_55); status: NEW; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/f8/cd05c47d78fbd7900b7a357cf1464a]
~> TaskHandler[id: 104; name: PROCESS_HITS (batch_1); status: NEW; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/63/bd937a11ebc30dc8da5c3a95ead187]
~> TaskHandler[id: 105; name: PROCESS_HITS (batch_37); status: NEW; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/e8/944485442dd75cf7232553335ab62c]
~> TaskHandler[id: 106; name: PROCESS_HITS (batch_42); status: NEW; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/07/ea1c3390ca85d2c1f6026b8e09d432]
~> TaskHandler[id: 107; name: PROCESS_HITS (batch_40); status: NEW; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/6a/99f45cb603b7187e0a7d74bdf03f5b]
~> TaskHandler[id: 108; name: PROCESS_HITS (batch_3); status: NEW; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/4e/d8ad5f85b4345991491145d2fb6204]
~> TaskHandler[id: 109; name: PROCESS_HITS (batch_36); status: NEW; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/c6/c50b75858725778782af18de8b590c]
~> TaskHandler[id: 110; name: PROCESS_HITS (batch_31); status: NEW; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/17/68706d80f55884dfde35dbabaf243d]
.. remaining tasks omitted.
May-21 02:17:49.530 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 93; name: PROCESS_HITS (batch_16); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/af/8a25e4e058e480d93730ea434968c1]
May-21 02:17:49.547 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:17:49.547 [Task submitter] INFO  nextflow.Session - [b1/d26371] Submitted process > PROCESS_HITS (batch_10)
May-21 02:17:49.756 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 94; name: PROCESS_HITS (batch_24); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/20/37346a9f851a8ed7982cd684c607dc]
May-21 02:17:49.793 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:17:49.793 [Task submitter] INFO  nextflow.Session - [ba/1ae731] Submitted process > PROCESS_HITS (batch_27)
May-21 02:17:49.930 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 96; name: PROCESS_HITS (batch_34); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/fe/6f9177d64e549adcc9970ece1958b3]
May-21 02:17:49.952 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:17:49.952 [Task submitter] INFO  nextflow.Session - [f8/cd05c4] Submitted process > PROCESS_HITS (batch_55)
May-21 02:17:50.175 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 95; name: PROCESS_HITS (batch_12); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/62/7a3336fdc6e5f7cd4f2e715a62ad59]
May-21 02:17:50.191 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:17:50.191 [Task submitter] INFO  nextflow.Session - [63/bd937a] Submitted process > PROCESS_HITS (batch_1)
May-21 02:17:51.364 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 97; name: PROCESS_HITS (batch_33); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/12/6d7c18dbcc9f27e866287ee01ab6fb]
May-21 02:17:51.379 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:17:51.379 [Task submitter] INFO  nextflow.Session - [e8/944485] Submitted process > PROCESS_HITS (batch_37)
May-21 02:17:51.637 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 99; name: PROCESS_HITS (batch_35); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/bf/c5abd00472de04dbb6fd6f7c38c3d2]
May-21 02:17:51.653 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:17:51.653 [Task submitter] INFO  nextflow.Session - [07/ea1c33] Submitted process > PROCESS_HITS (batch_42)
May-21 02:17:51.829 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 98; name: PROCESS_HITS (batch_2); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/ab/bf3ede54a977b54e5f028e8ba2d51b]
May-21 02:17:51.842 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:17:51.842 [Task submitter] INFO  nextflow.Session - [6a/99f45c] Submitted process > PROCESS_HITS (batch_40)
May-21 02:17:52.332 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 100; name: PROCESS_HITS (batch_20); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/7a/51bbdb1cf87499d134c2d27160ed27]
May-21 02:17:52.348 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:17:52.348 [Task submitter] INFO  nextflow.Session - [4e/d8ad5f] Submitted process > PROCESS_HITS (batch_3)
May-21 02:18:03.156 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 101; name: PROCESS_HITS (batch_10); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/b1/d26371a396d88e81cb0a0cf9d1db12]
May-21 02:18:03.175 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:18:03.175 [Task submitter] INFO  nextflow.Session - [c6/c50b75] Submitted process > PROCESS_HITS (batch_36)
May-21 02:18:03.712 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 104; name: PROCESS_HITS (batch_1); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/63/bd937a11ebc30dc8da5c3a95ead187]
May-21 02:18:03.728 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:18:03.728 [Task submitter] INFO  nextflow.Session - [17/68706d] Submitted process > PROCESS_HITS (batch_31)
May-21 02:18:03.824 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 102; name: PROCESS_HITS (batch_27); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/ba/1ae73197f4b12e2d25470881ab1b55]
May-21 02:18:03.843 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:18:03.844 [Task submitter] INFO  nextflow.Session - [95/89ab1b] Submitted process > PROCESS_HITS (batch_41)
May-21 02:18:04.080 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 103; name: PROCESS_HITS (batch_55); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/f8/cd05c47d78fbd7900b7a357cf1464a]
May-21 02:18:04.093 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:18:04.094 [Task submitter] INFO  nextflow.Session - [de/cc20d1] Submitted process > PROCESS_HITS (batch_45)
May-21 02:18:05.015 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 105; name: PROCESS_HITS (batch_37); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/e8/944485442dd75cf7232553335ab62c]
May-21 02:18:05.030 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:18:05.031 [Task submitter] INFO  nextflow.Session - [cf/5f0292] Submitted process > PROCESS_HITS (batch_47)
May-21 02:18:05.047 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 107; name: PROCESS_HITS (batch_40); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/6a/99f45cb603b7187e0a7d74bdf03f5b]
May-21 02:18:05.062 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:18:05.062 [Task submitter] INFO  nextflow.Session - [30/e64fc6] Submitted process > PROCESS_HITS (batch_44)
May-21 02:18:05.227 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 106; name: PROCESS_HITS (batch_42); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/07/ea1c3390ca85d2c1f6026b8e09d432]
May-21 02:18:05.240 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:18:05.240 [Task submitter] INFO  nextflow.Session - [63/2472f1] Submitted process > PROCESS_HITS (batch_43)
May-21 02:18:06.006 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 108; name: PROCESS_HITS (batch_3); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/4e/d8ad5f85b4345991491145d2fb6204]
May-21 02:18:06.022 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:18:06.022 [Task submitter] INFO  nextflow.Session - [6b/e5754b] Submitted process > PROCESS_HITS (batch_46)
May-21 02:18:17.058 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 109; name: PROCESS_HITS (batch_36); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/c6/c50b75858725778782af18de8b590c]
May-21 02:18:17.090 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:18:17.090 [Task submitter] INFO  nextflow.Session - [06/1a8d47] Submitted process > PROCESS_HITS (batch_51)
May-21 02:18:17.645 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 110; name: PROCESS_HITS (batch_31); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/17/68706d80f55884dfde35dbabaf243d]
May-21 02:18:17.668 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:18:17.669 [Task submitter] INFO  nextflow.Session - [07/5f5fdd] Submitted process > PROCESS_HITS (batch_49)
May-21 02:18:17.880 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 111; name: PROCESS_HITS (batch_41); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/95/89ab1b6353af6076109f01cdd614aa]
May-21 02:18:17.906 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:18:17.906 [Task submitter] INFO  nextflow.Session - [81/28f94a] Submitted process > PROCESS_HITS (batch_52)
May-21 02:18:18.492 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 112; name: PROCESS_HITS (batch_45); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/de/cc20d10a6806622cd87ffb729037ee]
May-21 02:18:18.519 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:18:18.519 [Task submitter] INFO  nextflow.Session - [f2/ce7c98] Submitted process > PROCESS_HITS (batch_48)
May-21 02:18:19.379 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 114; name: PROCESS_HITS (batch_44); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/30/e64fc6ef5f90142abe5c844478c9b3]
May-21 02:18:19.410 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:18:19.410 [Task submitter] INFO  nextflow.Session - [70/1f434d] Submitted process > PROCESS_HITS (batch_50)
May-21 02:18:19.423 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 115; name: PROCESS_HITS (batch_43); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/63/2472f1b07b20ab4965ae06d7cdd7af]
May-21 02:18:19.439 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:18:19.440 [Task submitter] INFO  nextflow.Session - [38/79e506] Submitted process > PROCESS_HITS (batch_53)
May-21 02:18:19.515 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 113; name: PROCESS_HITS (batch_47); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/cf/5f029215106ce8b146425fd6da75a3]
May-21 02:18:19.530 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:18:19.530 [Task submitter] INFO  nextflow.Session - [11/f1474d] Submitted process > PROCESS_HITS (batch_56)
May-21 02:18:20.370 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 116; name: PROCESS_HITS (batch_46); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/6b/e5754bbb6fb3acb70355ff34042b02]
May-21 02:18:20.395 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:18:20.395 [Task submitter] INFO  nextflow.Session - [9a/0105ab] Submitted process > PROCESS_HITS (batch_54)
May-21 02:18:31.745 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 117; name: PROCESS_HITS (batch_51); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/06/1a8d4724bb28fecc55b13be889536e]
May-21 02:18:31.784 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:18:31.784 [Task submitter] INFO  nextflow.Session - [62/15b4c8] Submitted process > PROCESS_HITS (batch_60)
May-21 02:18:33.190 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 119; name: PROCESS_HITS (batch_52); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/81/28f94acd08f6330b93ee33b4172b22]
May-21 02:18:33.212 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:18:33.212 [Task submitter] INFO  nextflow.Session - [9d/286090] Submitted process > PROCESS_HITS (batch_59)
May-21 02:18:33.286 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 118; name: PROCESS_HITS (batch_49); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/07/5f5fdd4364f64d38f22cacf0e890d2]
May-21 02:18:33.312 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:18:33.312 [Task submitter] INFO  nextflow.Session - [92/2a41cf] Submitted process > PROCESS_HITS (batch_61)
May-21 02:18:33.656 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 120; name: PROCESS_HITS (batch_48); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/f2/ce7c98525661f9c19dd7211404fa2f]
May-21 02:18:33.728 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:18:33.728 [Task submitter] INFO  nextflow.Session - [fa/aa6c2d] Submitted process > PROCESS_HITS (batch_57)
May-21 02:18:34.296 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 122; name: PROCESS_HITS (batch_53); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/38/79e506f6e25ad7e283163015da80ae]
May-21 02:18:34.319 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:18:34.320 [Task submitter] INFO  nextflow.Session - [a1/1f7566] Submitted process > PROCESS_HITS (batch_66)
May-21 02:18:34.439 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 121; name: PROCESS_HITS (batch_50); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/70/1f434d85588da87f9488a158469ff2]
May-21 02:18:34.513 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:18:34.513 [Task submitter] INFO  nextflow.Session - [5c/f0a8a8] Submitted process > PROCESS_HITS (batch_65)
May-21 02:18:34.617 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 123; name: PROCESS_HITS (batch_56); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/11/f1474d419ac1a88880eb333d7395da]
May-21 02:18:34.635 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:18:34.635 [Task submitter] INFO  nextflow.Session - [dd/01ee0d] Submitted process > PROCESS_HITS (batch_64)
May-21 02:18:35.411 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 124; name: PROCESS_HITS (batch_54); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/9a/0105ab4ba43d15cce872523038579e]
May-21 02:18:35.435 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:18:35.435 [Task submitter] INFO  nextflow.Session - [30/872f2e] Submitted process > PROCESS_HITS (batch_58)
May-21 02:18:36.407 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 129; name: PROCESS_HITS (batch_66); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/a1/1f7566162cff85a64104b77d34d00c]
May-21 02:18:36.419 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:18:36.419 [Task submitter] INFO  nextflow.Session - [0f/33728e] Submitted process > PROCESS_HITS (batch_63)
May-21 02:18:45.510 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 125; name: PROCESS_HITS (batch_60); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/62/15b4c840fec2f92e0269a4bd5b0383]
May-21 02:18:45.525 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:18:45.525 [Task submitter] INFO  nextflow.Session - [0c/8e66c6] Submitted process > PROCESS_HITS (batch_62)
May-21 02:18:46.216 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 126; name: PROCESS_HITS (batch_59); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/9d/286090553ba269f90c57b19e37043f]
May-21 02:18:46.331 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 128; name: PROCESS_HITS (batch_57); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/fa/aa6c2d50904cf8070dd2b580ca74e6]
May-21 02:18:46.637 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 127; name: PROCESS_HITS (batch_61); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/92/2a41cf20b052afd7834744a252d400]
May-21 02:18:47.545 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 131; name: PROCESS_HITS (batch_64); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/dd/01ee0deb3020d033f6aae400d7f2f3]
May-21 02:18:47.551 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 130; name: PROCESS_HITS (batch_65); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/5c/f0a8a8fcc4e7a8bf34bd80723240e7]
May-21 02:18:47.691 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 132; name: PROCESS_HITS (batch_58); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/30/872f2e0f9c49a34c27e293ae4e582f]
May-21 02:18:49.134 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 133; name: PROCESS_HITS (batch_63); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/0f/33728e185b7a96be8b5cd7824b5775]
May-21 02:18:57.647 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 134; name: PROCESS_HITS (batch_62); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/0c/8e66c66b570b12e7271e73cc0227ad]
May-21 02:22:13.307 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 2; name: GTDBTK (1); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/0c/4b939e79e55a58e7b27aaf1f4b6cdf]
May-21 02:22:13.344 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-21 02:22:13.344 [Task submitter] INFO  nextflow.Session - [eb/6adbb1] Submitted process > CREATE_DATAFRAME (1)
May-21 02:22:14.118 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 135; name: CREATE_DATAFRAME (1); status: COMPLETED; exit: 1; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/eb/6adbb165b04d37a5369fe5a3e5e7fc]
May-21 02:22:14.124 [TaskFinalizer-5] DEBUG nextflow.processor.TaskProcessor - Handling unexpected condition for
  task: name=CREATE_DATAFRAME (1); work-dir=/clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/eb/6adbb165b04d37a5369fe5a3e5e7fc
  error [nextflow.exception.ProcessFailedException]: Process `CREATE_DATAFRAME (1)` terminated with an error exit status (1)
May-21 02:22:14.136 [TaskFinalizer-5] ERROR nextflow.processor.TaskProcessor - Error executing process > 'CREATE_DATAFRAME (1)'

Caused by:
  Process `CREATE_DATAFRAME (1)` terminated with an error exit status (1)


Command executed:

  # Create directory structure expected by the script
  mkdir -p csv_hits
  for i in $(seq 1 66); do
      mkdir -p csv_hits/batch_$i
  done
  
  # Debug: List all CSV files
  echo "All CSV files to process:"
  ls -la main.nf || echo "No CSV files found"
  
  # Copy files from results/processed_hits directly
  echo "Copying files from results/processed_hits"
  for i in $(seq 1 66); do
      echo "Processing batch $i"
      # Use find to locate all CSV files in the results directory for this batch
      find results_GCF_025823245/processed_hits/batch_$i -name "*.csv" -exec cp {} csv_hits/batch_$i/ \;
  done
  
  # Debug: List the contents of the csv_hits directory
  echo "Contents of csv_hits directory:"
  find csv_hits -type f | sort
  
  # Run the script with the organized directory structure
  python create_unique_df_hits_optimized.py csv_hits gtdb_classification.csv final_results.csv

Command exit status:
  1

Command output:
  All CSV files to process:
  lrwxrwxrwx 1 laureli nogroup 83 May 21 02:22 main.nf -> /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/main.nf
  Copying files from results/processed_hits
  Processing batch 1

Command error:
  INFO:    Environment variable SINGULARITYENV_TMPDIR is set, but APPTAINERENV_TMPDIR is preferred
  INFO:    Environment variable SINGULARITYENV_NXF_TASK_WORKDIR is set, but APPTAINERENV_NXF_TASK_WORKDIR is preferred
  All CSV files to process:
  lrwxrwxrwx 1 laureli nogroup 83 May 21 02:22 main.nf -> /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/main.nf
  Copying files from results/processed_hits
  Processing batch 1
  find: 'results_GCF_025823245/processed_hits/batch_1': No such file or directory

Work dir:
  /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/eb/6adbb165b04d37a5369fe5a3e5e7fc

Container:
  /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/containers/hmmer.sif

Tip: view the complete command output by changing to the process work dir and entering the command `cat .command.out`
May-21 02:22:14.139 [TaskFinalizer-5] DEBUG nextflow.Session - Session aborted -- Cause: Process `CREATE_DATAFRAME (1)` terminated with an error exit status (1)
May-21 02:22:14.139 [main] DEBUG nextflow.Session - Session await > all processes finished
May-21 02:22:14.150 [main] DEBUG nextflow.Session - Session await > all barriers passed
May-21 02:22:14.150 [Task monitor] DEBUG n.processor.TaskPollingMonitor - <<< barrier arrives (monitor: local) - terminating tasks monitor poll loop
May-21 02:22:14.162 [main] DEBUG n.trace.WorkflowStatsObserver - Workflow completed > WorkflowStats[succeededCount=134; failedCount=1; ignoredCount=0; cachedCount=0; pendingCount=0; submittedCount=0; runningCount=0; retriesCount=0; abortedCount=0; succeedDuration=6h 53m 4s; failedDuration=2.8s; cachedDuration=0ms;loadCpus=0; loadMemory=0; peakRunning=12; peakCpus=38; peakMemory=76 GB; ]
May-21 02:22:14.355 [main] DEBUG nextflow.cache.CacheDB - Closing CacheDB done
May-21 02:22:14.401 [main] DEBUG nextflow.script.ScriptRunner - > Execution complete -- Goodbye
