#!/bin/bash -ue
# Create directory structure expected by the script
mkdir -p csv_hits
for i in $(seq 1 4); do
    mkdir -p csv_hits/batch_$i
done

# Debug: List all CSV files
echo "All CSV files to process:"
ls -la main.nf || echo "No CSV files found"

# Get the project root directory (where main.nf is located)
PROJECT_ROOT=$(dirname $(readlink -f main.nf))
echo "Project root: $PROJECT_ROOT"

# Get the output directory path
OUTDIR="$PROJECT_ROOT/results"
echo "Output directory: $OUTDIR"

# Copy files from the output directory's processed_hits directly
echo "Copying files from $OUTDIR/processed_hits"

# Check if the directory exists
if [ ! -d "$OUTDIR/processed_hits" ]; then
    echo "Directory $OUTDIR/processed_hits does not exist"
    echo "Current directory: $(pwd)"
    echo "Listing current directory:"
    ls -la
    echo "Listing project root:"
    ls -la $PROJECT_ROOT
    exit 1
fi

# Find all batch directories in the processed_hits directory
BATCH_DIRS=$(find $OUTDIR/processed_hits -maxdepth 1 -type d -name "batch_*" | sort)

if [ -z "$BATCH_DIRS" ]; then
    echo "No batch directories found in $OUTDIR/processed_hits"
    exit 1
fi

# Process each batch directory
for BATCH_DIR in $BATCH_DIRS; do
    BATCH_NAME=$(basename $BATCH_DIR)
    echo "Processing $BATCH_NAME"

    # Extract batch number
    BATCH_NUM=$(echo $BATCH_NAME | sed 's/batch_//')

    # Create the corresponding directory in csv_hits if it doesn't exist
    mkdir -p csv_hits/$BATCH_NAME

    # Copy CSV files using absolute paths
    find "$OUTDIR/processed_hits/$BATCH_NAME" -name "*.csv" -exec cp {} csv_hits/$BATCH_NAME/ \;
done

# Debug: List the contents of the csv_hits directory
echo "Contents of csv_hits directory:"
find csv_hits -type f | sort

# Run the script with the organized directory structure
python create_unique_df_hits_optimized.py csv_hits gtdb_classification.csv final_results.csv
