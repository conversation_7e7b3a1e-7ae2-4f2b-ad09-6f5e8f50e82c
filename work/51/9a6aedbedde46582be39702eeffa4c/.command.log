INFO:    Environment variable SINGULARITYENV_TMPDIR is set, but APPTAINERENV_TMPDIR is preferred
INFO:    Environment variable SINGULARITYENV_NXF_TASK_WORKDIR is set, but APPTAINERENV_NXF_TASK_WORKDIR is preferred
All CSV files to process:
lrwxrwxrwx 1 laureli nogroup 83 May 27 09:58 main.nf -> /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/main.nf
Project root: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow
Output directory: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/results
Copying files from /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/results/processed_hits
Directory /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/results/processed_hits does not exist
Current directory: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/51/9a6aedbedde46582be39702eeffa4c
Listing current directory:
total 10
drwxrwsr-x 2 laureli nogroup 4096 May 27 09:58 .
drwxrwsr-x 2 laureli nogroup 4096 May 27 09:58 ..
-rw-rw-r-- 1 <USER> <GROUP>    0 May 27 09:58 .command.begin
-rw-rw-r-- 1 <USER> <GROUP>  214 May 27 09:58 .command.err
-rw-rw-r-- 1 <USER> <GROUP>  975 May 27 09:58 .command.log
-rw-rw-r-- 1 <USER> <GROUP>  761 May 27 09:58 .command.out
-rw-rw-r-- 1 <USER> <GROUP> 4642 May 27 09:58 .command.run
-rw-rw-r-- 1 <USER> <GROUP> 1899 May 27 09:58 .command.sh
lrwxrwxrwx 1 laureli nogroup  114 May 27 09:58 create_unique_df_hits_optimized.py -> /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/bin/create_unique_df_hits_optimized.py
drwxrwsr-x 2 laureli nogroup 4096 May 27 09:58 csv_hits
lrwxrwxrwx 1 laureli nogroup  138 May 27 09:58 gtdb_classification.csv -> /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/66/9c48b871834a59550f931292a5b8ef/gtdb_classification.csv
lrwxrwxrwx 1 laureli nogroup   83 May 27 09:58 main.nf -> /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/main.nf
Listing project root:
total 9484559
drwxrwsr-x 2 laureli nogroup       4096 May 27 09:58 .
drwxrwsr-x 2 laureli nogroup       4096 May 19 05:29 ..
drwxrwsr-x 2 laureli nogroup       4096 May 27 09:58 .nextflow
-rw-rw-r-- 1 <USER> <GROUP>      11479 May 27 09:58 .nextflow.log
-rw-rw-r-- 1 <USER> <GROUP>      24065 May 27 09:53 .nextflow.log.1
-rw-rw-r-- 1 <USER> <GROUP>      24048 May 27 09:53 .nextflow.log.2
-rw-rw-r-- 1 <USER> <GROUP>    4190374 May 19 05:48 GCF_008369605.1.fna
-rw-rw-r-- 1 <USER> <GROUP>    4809616 Oct 26  2022 GCF_025823245.1.fna
-rw-rw-r-- 1 <USER> <GROUP>       4872 May 19 22:26 README.md
-rw-rw-r-- 1 <USER> <GROUP>      12855 May 20 12:42 autoencoder_tool.py
drwxrwsr-x 2 laureli nogroup       4096 May 19 09:22 backup
drwxrwsr-x 2 laureli nogroup       4096 May 22 04:29 backup_parallel
-rw-rw-r-- 1 <USER> <GROUP>     487562 May 20 13:56 best_refined_model_RS.pkl
drwxrwsr-x 2 laureli nogroup       4096 May 19 07:56 bin
-rwxrwxr-x 1 <USER> <GROUP>        712 May 19 07:56 build_containers.sh
-rw-rw-r-- 1 <USER> <GROUP>       5569 May 20 12:19 check_compatibility.py
drwxrwsr-x 2 laureli nogroup       4096 May 19 08:55 containers
-rw-rw-r-- 1 <USER> <GROUP>       8199 May 19 05:37 convert_hits.py
-rw-rw-r-- 1 <USER> <GROUP>       9583 May 19 05:31 create_unique_df_hits_optimized.py
-rw-rw-r-- 1 <USER> <GROUP>       5846 May 20 12:19 encode_final_results.py
-rwxrwxr-x 1 <USER> <GROUP>       1612 May 21 02:28 fix_workflow.sh
-rwxrwxr-x 1 <USER> <GROUP>       4552 May 19 05:30 hmmsearch_python.py
-rw-rw-r-- 1 <USER> <GROUP>      12096 May 27 09:11 main.nf
-rw-rw-r-- 1 <USER> <GROUP>      10864 May 21 09:56 main.nf.fixed
drwxrwsr-x 2 laureli nogroup       4096 May 27 08:23 ml
-rw-rw-r-- 1 <USER> <GROUP>       9532 May 20 13:56 model_load_and_predict.py
-rw-rw-r-- 1 <USER> <GROUP>       1921 May 20 09:20 nextflow.config
drwxrwsr-x 2 laureli nogroup       4096 May 22 05:06 ppred
-rwxrwxr-x 1 <USER> <GROUP>       1741 May 20 10:39 process_existing_results.sh
-rw-rw-r-- 1 <USER> <GROUP>       2739 May 20 10:31 process_results.py
-rw-rw-r-- 1 <USER> <GROUP>       1231 May 19 07:11 project_summary.txt
-rw-rw-r-- 1 <USER> <GROUP>         22 May 22 04:09 remove.log
-rw-rw-r-- 1 <USER> <GROUP>    2944023 May 27 09:15 report-20250527-33328296.html
drwxrwsr-x 2 laureli nogroup       4096 May 27 09:58 results
-rw-rw-r-- 1 <USER> <GROUP> 9698149560 May 20 12:44 robustscaler_enc1024_layers1.h5
-rw-rw-r-- 1 <USER> <GROUP>    1171829 May 20 12:43 robustscaler_enc1024_layers1.pkl
-rw-rw-r-- 1 <USER> <GROUP>       2720 May 20 10:34 run_final_step.py
-rwxrwxr-x 1 <USER> <GROUP>      11673 May 19 05:35 run_gtdbtk.py
-rwxrwxr-x 1 <USER> <GROUP>       6093 May 19 05:29 run_prodigal_and_rename.py
-rw-rw-r-- 1 <USER> <GROUP>        261 May 19 23:33 test_path.nf
-rw-rw-r-- 1 <USER> <GROUP>     252307 May 27 09:15 timeline-20250527-33328296.html
drwxrwsr-x 2 laureli nogroup       4096 May 27 09:58 work
